# Remote Config Keys Documentation

This document provides a comprehensive list of all Firebase Remote Config keys used in the GT+ Flutter application.

## Version Control Keys

1. **MIN_VERSION_GT_DEV**: Minimum app version required for development environment
2. **MIN_VERSION_GT_PROD**: Minimum app version required for production environment

## Timer Configuration Keys

3. **TIMER_IN_SECONDS_GT_DEV**: Timer duration in seconds for GT operations in development environment
4. **TIMER_IN_SECONDS_GT_PROD**: Timer duration in seconds for GT operations in production environment

## Feature Control Keys

5. **IS_FLASH_NEEDED_GT**: Boolean flag to control whether camera flash is required for GT operations

## Content Configuration Keys

6. **INSTRUCTIONS_GT**: JSON configuration containing GT instructions and guidance text
7. **AUDIOMETRY_PROMPT_GT**: Text prompt displayed during audiometry testing procedures
8. **COGNIVUE_PROMPT_GT**: Text prompt displayed during Cognivue cognitive assessment procedures

## Questionnaire Data Keys

9. **QUESTIONNAIRES_STAGING**: JSON configuration for questionnaires in staging environment containing Audiology questionnaires
10. **QUESTIONNAIRES_PRODUCTION**: JSON configuration for questionnaires in production environment containing Audiology questionnaires

## Key Usage Notes

- **Environment-based keys**: The app automatically selects between DEV and PROD variants based on the current environment (`isProdEnv` flag)
- **Default values**: All keys have default fallback values set during Firebase Remote Config initialization
- **Data formats**: 
  - Version keys return strings (e.g., "1.0.0")
  - Timer keys return integers (default: 300 seconds)
  - Feature flags return booleans
  - Content keys return JSON strings that are parsed into models
- **Questionnaire structure**: The questionnaire data contains nested JSON with `gt-ipad-config` and `Audiology` sections for different assessment types

## Implementation Details

All remote config keys are defined as constants in the `FirebaseRemoteConfigKey` class located in `lib/services/remoteConfig/firebase_remote_config_service.dart`. The service provides typed getter methods for each configuration value and handles environment-specific key selection automatically.
